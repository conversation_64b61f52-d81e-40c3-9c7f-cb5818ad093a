<template>
  <uni-nav-bar
    :style="{ position: fixed ? 'fixed' : '', 'z-index': '99' }"
    left-icon="left"
    :title="title"
    :background-color="backgroundColor"
    :color="frontColor"
    :left-width="leftWidth"
    :right-width="rightWidth"
    :border="false"
    status-bar
    :fixed="fixed"
  >
    <template #left>
      <view class="navbar-left">
        <view v-if="showNavBack" class="nav-back" hover-class="none" @tap="handleNavBack">
          <uni-icons type="back" size="24" :color="defaultColor || frontColor"></uni-icons>
        </view>
        <navigator
          v-else-if="showHome"
          class="nav-back"
          open-type="switchTab"
          url="/pages/index/index"
          hover-class="none"
        >
          <uni-icons
            type="home-filled"
            size="24"
            :color="defaultColor ? defaultColor : iconWord ? '#fff' : frontColor"
          ></uni-icons>
          <text v-if="iconWord" class="iconWord" :style="{ color: defaultColor ? defaultColor : '#fff' }">{{
            iconWord
          }}</text>
        </navigator>
        <view v-if="showBusSwitch || showQrCode" class="navbar-items">
          <view v-if="showQrCode" class="navbar-item" @tap.stop="handleQrCode">
            <image
              class="switch-img icon-mr"
              mode="scaleToFill"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/qrcode-dark.png"
            />
            入场凭证
          </view>
          <view v-if="showBusSwitch" class="navbar-item" @tap="switchBus">
            <ThemeIcon class="icon-mr" type="t-icon-mendianqiehuan-01" :size="15" color="#000" />
            {{ busOrMerName }}
          </view>
        </view>
      </view>
    </template>
  </uni-nav-bar>
</template>

<script setup lang="ts" name="index">
import { useThemeStore } from '@/store/theme'
import { useUserStore } from '@/store/user'
import { isTabBarPage } from '@/utils/urlMap'

const props = defineProps({
  defaultColor: {
    type: String,
    default: '',
  },
  backgroundColor: {
    type: String,
    default: 'transparent',
  },
  iconWord: {
    type: String,
    default: '',
  },
  showBusSwitch: {
    type: Boolean,
    default: false,
  },
  showQrCode: {
    type: Boolean,
    default: false,
  },
  fixed: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: '',
  },
  leftWidth: {
    type: [Number, String],
    default: '200rpx',
  },
  rightWidth: {
    type: [Number, String],
    default: '200rpx',
  },
})
const emits = defineEmits(['click-qrcode'])
const themeStore = useThemeStore()
const frontColor = computed(() => (themeStore.theme1.background_color === 2 ? '#ffffff' : '#000000'))
const userBusName = computed(() => {
  return userStore.userInfo.bus_name
})
const userStore = useUserStore()
const busOrMerName = computed(() => {
  const busNameValue = themeStore.isShowMerchantMode ? userStore.userInfo.merchant_name : userBusName.value
  const maxLength = 7
  return busNameValue.length > maxLength ? '...' + busNameValue.slice(-maxLength) : busNameValue
})
// eslint-disable-next-line no-undef
const pages = getCurrentPages()
const showNavBack = computed(() => {
  // 有多级路由 或者在tabbar页面并且运营模式为综合体育场馆模式且显示模式为商家的时候展示返回按钮
  return (
    (pages.length && pages.length > 1) ||
    (isTabBarPage() && !themeStore.isShowMerchantMode && themeStore.operationMode === 1)
  )
})
const showHome = ref(!isTabBarPage() && !showNavBack.value)

function handleNavBack() {
  if (pages.length && pages.length > 1) {
    uni.navigateBack()
  } else {
    const page = pages[pages.length - 1]
    const currentPagePath = page.route
    const shouldBackToBusPage = uni.getStorageSync('shouldBackToBusPage')
    uni.switchTab({
      url: `/pages/index/${shouldBackToBusPage && currentPagePath !== 'pages/index/bus' ? 'bus' : 'index'}`,
    })
  }
}
function switchBus() {
  uni.navigateTo({
    url: '/pages/busSelect',
  })
}
function handleQrCode() {
  emits('click-qrcode')
  uni.navigateTo({
    url: '/pages/my/ticket-index?from=index',
  })
}
</script>

<style lang="scss" scoped>
.navbar-left {
  display: flex;
  align-items: center;
}
.navbar-items {
  border-radius: 31rpx;
  padding: 0 10rpx;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  .navbar-item {
    position: relative;
    padding: 8rpx 10rpx;
    display: flex;
    align-items: center;
    &:last-child::after {
      display: none;
    }
  }
}
.switch-img {
  width: 36rpx;
  height: 36rpx;
}
.nav-back {
  padding: 8rpx;
  padding-left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.title-bar-text {
  flex: 1;
  margin-right: 220rpx;
  text-align: center;
  font-size: 30rpx;
}
.iconWord {
  font-size: 30rpx;
  color: #fff;
  margin-left: 8rpx;
}
</style>
